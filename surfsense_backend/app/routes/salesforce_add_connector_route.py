# app/routes/salesforce_add_connector_route.py
import base64
import json
import logging
import urllib.parse
from uuid import UUID

import httpx
from fastapi import APIRouter, Depends, HTTPException, Request
from fastapi.responses import RedirectResponse
from pydantic import ValidationError
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from app.config import config
from app.db import (
    SearchSourceConnector,
    SearchSourceConnectorType,
    User,
    get_async_session,
)
from app.users import current_active_user

logger = logging.getLogger(__name__)

router = APIRouter()

# Salesforce OAuth endpoints
SALESFORCE_AUTH_URL = "https://login.salesforce.com/services/oauth2/authorize"
SALESFORCE_TOKEN_URL = "https://login.salesforce.com/services/oauth2/token"

# Salesforce OAuth scopes
SCOPES = ["api", "refresh_token", "offline_access"]
REDIRECT_URI = config.SALESFORCE_REDIRECT_URI


@router.get("/auth/salesforce/connector/add/")
async def connect_salesforce(space_id: int, user: User = Depends(current_active_user)):
    """
    Initiate Salesforce OAuth flow for connector setup.
    """
    try:
        if not space_id:
            raise HTTPException(status_code=400, detail="space_id is required")

        if not config.SALESFORCE_CLIENT_ID or not config.SALESFORCE_CLIENT_SECRET:
            raise HTTPException(
                status_code=500, 
                detail="Salesforce OAuth credentials not configured"
            )

        # Encode space_id and user_id in state
        state_payload = json.dumps(
            {
                "space_id": space_id,
                "user_id": str(user.id),
            }
        )
        state_encoded = base64.urlsafe_b64encode(state_payload.encode()).decode()

        # Build authorization URL
        auth_params = {
            "response_type": "code",
            "client_id": config.SALESFORCE_CLIENT_ID,
            "redirect_uri": REDIRECT_URI,
            "scope": " ".join(SCOPES),
            "state": state_encoded,
            "prompt": "consent",
        }

        auth_url = f"{SALESFORCE_AUTH_URL}?{urllib.parse.urlencode(auth_params)}"
        
        logger.info(f"Generated Salesforce auth URL for user {user.id}, space {space_id}")
        return {"auth_url": auth_url}
        
    except Exception as e:
        logger.error(f"Failed to initiate Salesforce OAuth: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to initiate Salesforce OAuth: {e!s}"
        ) from e


@router.get("/auth/salesforce/connector/callback/")
async def salesforce_callback(
    request: Request,
    code: str,
    state: str,
    session: AsyncSession = Depends(get_async_session),
):
    """
    Handle Salesforce OAuth callback and create connector.
    """
    try:
        # Decode and parse the state
        decoded_state = base64.urlsafe_b64decode(state.encode()).decode()
        data = json.loads(decoded_state)

        user_id = UUID(data["user_id"])
        space_id = data["space_id"]

        # Exchange authorization code for access token
        token_data = {
            "grant_type": "authorization_code",
            "client_id": config.SALESFORCE_CLIENT_ID,
            "client_secret": config.SALESFORCE_CLIENT_SECRET,
            "redirect_uri": REDIRECT_URI,
            "code": code,
        }

        async with httpx.AsyncClient() as client:
            token_response = await client.post(
                SALESFORCE_TOKEN_URL,
                data=token_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"},
            )

        if token_response.status_code != 200:
            logger.error(f"Salesforce token exchange failed: {token_response.text}")
            raise HTTPException(
                status_code=400, 
                detail=f"Failed to exchange code for token: {token_response.text}"
            )

        token_info = token_response.json()
        
        # Extract token information
        access_token = token_info.get("access_token")
        refresh_token = token_info.get("refresh_token")
        instance_url = token_info.get("instance_url")
        
        if not access_token or not instance_url:
            raise HTTPException(
                status_code=400, 
                detail="Invalid token response from Salesforce"
            )

        # Prepare credentials for storage
        creds_dict = {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "instance_url": instance_url,
            "client_id": config.SALESFORCE_CLIENT_ID,
            "client_secret": config.SALESFORCE_CLIENT_SECRET,
            "token_type": token_info.get("token_type", "Bearer"),
            "scope": token_info.get("scope"),
            "issued_at": token_info.get("issued_at"),
            "signature": token_info.get("signature"),
        }

        try:
            # Check if a connector with the same type already exists for this user
            result = await session.execute(
                select(SearchSourceConnector).filter(
                    SearchSourceConnector.user_id == user_id,
                    SearchSourceConnector.connector_type
                    == SearchSourceConnectorType.SALESFORCE_CONNECTOR,
                )
            )
            existing_connector = result.scalars().first()
            if existing_connector:
                raise HTTPException(
                    status_code=409,
                    detail="A SALESFORCE_CONNECTOR connector already exists. Each user can have only one connector of each type.",
                )

            # Create new connector
            db_connector = SearchSourceConnector(
                name="Salesforce Connector",
                connector_type=SearchSourceConnectorType.SALESFORCE_CONNECTOR,
                config=creds_dict,
                user_id=user_id,
                is_indexable=True,
            )
            session.add(db_connector)
            await session.commit()
            await session.refresh(db_connector)
            
            logger.info(f"Created Salesforce connector {db_connector.id} for user {user_id}")
            
            return RedirectResponse(
                f"{config.NEXT_FRONTEND_URL}/dashboard/{space_id}/connectors/add/salesforce-connector?success=true"
            )
            
        except ValidationError as e:
            await session.rollback()
            logger.error(f"Validation error creating Salesforce connector: {e}")
            raise HTTPException(
                status_code=422, detail=f"Validation error: {e!s}"
            ) from e
        except IntegrityError as e:
            await session.rollback()
            logger.error(f"Integrity error creating Salesforce connector: {e}")
            raise HTTPException(
                status_code=409,
                detail=f"Integrity error: A connector with this type already exists. {e!s}",
            ) from e
        except HTTPException:
            await session.rollback()
            raise
        except Exception as e:
            logger.error(f"Failed to create Salesforce connector: {e}")
            await session.rollback()
            raise HTTPException(
                status_code=500,
                detail=f"Failed to create search source connector: {e!s}",
            ) from e

    except Exception as e:
        logger.error(f"Failed to complete Salesforce OAuth: {e}")
        raise HTTPException(
            status_code=500, detail=f"Failed to complete Salesforce OAuth: {e!s}"
        ) from e
