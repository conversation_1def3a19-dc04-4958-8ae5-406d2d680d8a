"""
Salesforce Connector Module | Salesforce OAuth Credentials | Salesforce REST API
A module for retrieving data from Salesforce using OAuth credentials.
Allows fetching standard Salesforce objects like Accounts, Contacts, Opportunities, etc.
"""

import json
import logging
from datetime import datetime
from typing import Any

from simple_salesforce import Salesforce

logger = logging.getLogger(__name__)


class SalesforceConnector:
    """Class for retrieving data from Salesforce using OAuth credentials."""

    def __init__(
        self,
        instance_url: str,
        access_token: str,
        refresh_token: str | None = None,
        client_id: str | None = None,
        client_secret: str | None = None,
    ):
        """
        Initialize the SalesforceConnector class.
        Args:
            instance_url: Salesforce instance URL
            access_token: OAuth access token
            refresh_token: OAuth refresh token (optional)
            client_id: OAuth client ID (optional)
            client_secret: OAuth client secret (optional)
        """
        self.instance_url = instance_url
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.client_id = client_id
        self.client_secret = client_secret
        self._sf_client = None

    def _get_client(self) -> Salesforce:
        """
        Get the Salesforce client instance using OAuth credentials.
        Returns:
            Salesforce client instance
        Raises:
            Exception: If client creation fails
        """
        if self._sf_client:
            return self._sf_client

        try:
            self._sf_client = Salesforce(
                instance_url=self.instance_url,
                session_id=self.access_token,
            )
            return self._sf_client
        except Exception as e:
            logger.error(f"Failed to create Salesforce client: {e}")
            raise Exception(f"Failed to create Salesforce client: {e!s}") from e

    def test_connection(self) -> tuple[bool, str | None]:
        """
        Test the Salesforce connection.
        Returns:
            Tuple containing (success boolean, error message or None)
        """
        try:
            sf = self._get_client()
            # Try to get user info to test connection
            user_info = sf.query("SELECT Id, Name, Email FROM User WHERE Id = UserInfo.getUserId()")
            if user_info.get("records"):
                return True, None
            return False, "No user information returned"
        except Exception as e:
            logger.error(f"Salesforce connection test failed: {e}")
            return False, f"Connection test failed: {e!s}"

    def get_accounts(self, limit: int = 1000) -> tuple[list[dict[str, Any]], str | None]:
        """
        Fetch Salesforce Accounts.
        Args:
            limit: Maximum number of records to fetch
        Returns:
            Tuple containing (accounts list, error message or None)
        """
        try:
            sf = self._get_client()
            query = f"""
                SELECT Id, Name, Type, Industry, BillingStreet, BillingCity, 
                       BillingState, BillingCountry, Phone, Website, Description,
                       CreatedDate, LastModifiedDate
                FROM Account 
                ORDER BY LastModifiedDate DESC 
                LIMIT {limit}
            """
            result = sf.query(query)
            return result.get("records", []), None
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce accounts: {e}")
            return [], f"Error fetching accounts: {e!s}"

    def get_contacts(self, limit: int = 1000) -> tuple[list[dict[str, Any]], str | None]:
        """
        Fetch Salesforce Contacts.
        Args:
            limit: Maximum number of records to fetch
        Returns:
            Tuple containing (contacts list, error message or None)
        """
        try:
            sf = self._get_client()
            query = f"""
                SELECT Id, FirstName, LastName, Email, Phone, Title, Department,
                       Account.Name, MailingStreet, MailingCity, MailingState, 
                       MailingCountry, Description, CreatedDate, LastModifiedDate
                FROM Contact 
                ORDER BY LastModifiedDate DESC 
                LIMIT {limit}
            """
            result = sf.query(query)
            return result.get("records", []), None
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce contacts: {e}")
            return [], f"Error fetching contacts: {e!s}"

    def get_opportunities(self, limit: int = 1000) -> tuple[list[dict[str, Any]], str | None]:
        """
        Fetch Salesforce Opportunities.
        Args:
            limit: Maximum number of records to fetch
        Returns:
            Tuple containing (opportunities list, error message or None)
        """
        try:
            sf = self._get_client()
            query = f"""
                SELECT Id, Name, StageName, Amount, CloseDate, Probability,
                       Account.Name, Type, LeadSource, Description,
                       CreatedDate, LastModifiedDate
                FROM Opportunity 
                ORDER BY LastModifiedDate DESC 
                LIMIT {limit}
            """
            result = sf.query(query)
            return result.get("records", []), None
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce opportunities: {e}")
            return [], f"Error fetching opportunities: {e!s}"

    def get_leads(self, limit: int = 1000) -> tuple[list[dict[str, Any]], str | None]:
        """
        Fetch Salesforce Leads.
        Args:
            limit: Maximum number of records to fetch
        Returns:
            Tuple containing (leads list, error message or None)
        """
        try:
            sf = self._get_client()
            query = f"""
                SELECT Id, FirstName, LastName, Email, Phone, Company, Title,
                       Status, LeadSource, Street, City, State, Country,
                       Description, CreatedDate, LastModifiedDate
                FROM Lead 
                ORDER BY LastModifiedDate DESC 
                LIMIT {limit}
            """
            result = sf.query(query)
            return result.get("records", []), None
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce leads: {e}")
            return [], f"Error fetching leads: {e!s}"

    def get_cases(self, limit: int = 1000) -> tuple[list[dict[str, Any]], str | None]:
        """
        Fetch Salesforce Cases.
        Args:
            limit: Maximum number of records to fetch
        Returns:
            Tuple containing (cases list, error message or None)
        """
        try:
            sf = self._get_client()
            query = f"""
                SELECT Id, CaseNumber, Subject, Status, Priority, Origin,
                       Account.Name, Contact.Name, Description, 
                       CreatedDate, LastModifiedDate
                FROM Case 
                ORDER BY LastModifiedDate DESC 
                LIMIT {limit}
            """
            result = sf.query(query)
            return result.get("records", []), None
        except Exception as e:
            logger.error(f"Failed to fetch Salesforce cases: {e}")
            return [], f"Error fetching cases: {e!s}"

    def get_all_objects(self, limit_per_object: int = 500) -> tuple[dict[str, list[dict[str, Any]]], str | None]:
        """
        Fetch all supported Salesforce objects.
        Args:
            limit_per_object: Maximum number of records to fetch per object type
        Returns:
            Tuple containing (dictionary of object types to records, error message or None)
        """
        all_objects = {}
        errors = []

        # Fetch each object type
        object_methods = [
            ("accounts", self.get_accounts),
            ("contacts", self.get_contacts),
            ("opportunities", self.get_opportunities),
            ("leads", self.get_leads),
            ("cases", self.get_cases),
        ]

        for object_name, method in object_methods:
            try:
                records, error = method(limit_per_object)
                if error:
                    errors.append(f"{object_name}: {error}")
                else:
                    all_objects[object_name] = records
                    logger.info(f"Fetched {len(records)} {object_name}")
            except Exception as e:
                errors.append(f"{object_name}: {e!s}")
                logger.error(f"Error fetching {object_name}: {e}")

        error_message = "; ".join(errors) if errors else None
        return all_objects, error_message

    @staticmethod
    def format_record_as_markdown(record: dict[str, Any], object_type: str) -> str:
        """
        Format a Salesforce record as markdown content.
        Args:
            record: Salesforce record dictionary
            object_type: Type of Salesforce object (account, contact, etc.)
        Returns:
            Formatted markdown string
        """
        # Remove attributes field that Salesforce adds
        clean_record = {k: v for k, v in record.items() if k != "attributes"}
        
        # Create title based on object type
        if object_type == "account":
            title = clean_record.get("Name", "Unknown Account")
        elif object_type == "contact":
            first_name = clean_record.get("FirstName", "")
            last_name = clean_record.get("LastName", "")
            title = f"{first_name} {last_name}".strip() or "Unknown Contact"
        elif object_type == "opportunity":
            title = clean_record.get("Name", "Unknown Opportunity")
        elif object_type == "lead":
            first_name = clean_record.get("FirstName", "")
            last_name = clean_record.get("LastName", "")
            title = f"{first_name} {last_name}".strip() or "Unknown Lead"
        elif object_type == "case":
            title = clean_record.get("Subject", f"Case {clean_record.get('CaseNumber', 'Unknown')}")
        else:
            title = f"Salesforce {object_type.title()}"

        markdown_content = f"# {title}\n\n"
        markdown_content += f"**Object Type:** {object_type.title()}\n"
        markdown_content += f"**Salesforce ID:** {clean_record.get('Id', 'N/A')}\n\n"

        # Add key fields based on object type
        for key, value in clean_record.items():
            if key == "Id":
                continue  # Already added above
            
            # Handle nested objects (like Account.Name in Contact)
            if isinstance(value, dict) and value is not None:
                for nested_key, nested_value in value.items():
                    if nested_key != "attributes" and nested_value is not None:
                        markdown_content += f"**{key}.{nested_key}:** {nested_value}\n"
            elif value is not None and str(value).strip():
                # Format field names nicely
                formatted_key = key.replace("_", " ").title()
                markdown_content += f"**{formatted_key}:** {value}\n"

        return markdown_content
